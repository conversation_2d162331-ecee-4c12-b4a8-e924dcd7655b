# 时间线换行功能说明

## 功能概述

已为时间线和产品信息模块添加了自动换行功能，当数据中包含 `\n` 字符时，系统会自动将其转换为真正的换行显示。

## 实现原理

### 1. 时间线模块换行处理

在 `generateDynamicTimeline` 函数中，对文本类型的数据进行了特殊处理：

```javascript
// 文本类型 - 处理换行符
if (typeof value === 'string' && value.includes('\\n')) {
    // 如果包含 \n，将其转换为真正的换行
    const lines = value.split('\\n');
    lines.forEach((line, lineIndex) => {
        if (lineIndex > 0) {
            // 添加换行元素
            detailValue.appendChild(document.createElement('br'));
        }
        // 添加文本节点
        const textNode = document.createTextNode(line);
        detailValue.appendChild(textNode);
    });
} else {
    // 普通文本，直接设置
    detailValue.textContent = value;
}
```

### 2. 产品信息模块换行处理

在 `updateProductInfo` 函数中，对包含换行符的文本使用 `textarea` 元素：

```javascript
// 文本类型 - 检查是否包含换行符
if (typeof propertyValue === 'string' && propertyValue.includes('\\n')) {
    // 包含换行符，使用textarea
    const textareaValue = propertyValue.replace(/\\n/g, '\n');
    formRowsHtml += `
        <div class="form-row">
            <label class="form-label">${propertyName}</label>
            <textarea class="form-input form-textarea" readonly>${textareaValue}</textarea>
        </div>
    `;
} else {
    // 普通文本，使用input
    formRowsHtml += `
        <div class="form-row">
            <label class="form-label">${propertyName}</label>
            <input type="text" class="form-input" value="${propertyValue}" readonly>
        </div>
    `;
}
```

## CSS样式优化

### 1. 时间线样式

为 `.detail-value` 添加了 `white-space: pre-wrap` 属性，确保换行和空格得到保持：

```css
.detail-value {
  color: #5D4E37;
  font-size: 0.875rem;
  line-height: 1.6;
  text-align: left;
  word-break: break-word;
  flex: 1;
  margin-left: 0.5rem;
  white-space: pre-wrap; /* 保持换行和空格 */
}
```

### 2. 表单textarea样式

为 `.form-textarea` 添加了专门的样式：

```css
.form-textarea {
    min-height: 60px !important;
    resize: vertical !important;
    font-family: inherit !important;
    line-height: 1.5 !important;
    white-space: pre-wrap !important;
}
```

## 数据格式要求

### 输入数据格式

当API返回的数据中包含需要换行的文本时，应使用 `\\n` 作为换行标记。例如：

```javascript
{
    "生产过程信息_品名": "白术\\n批号: 20250220091",
    "生产过程信息_操作人": "张三\\n复核人: 李四",
    "开始验证_提醒": "品名: 白术\\n批号: 20250220091\\n日期: 2025年6月1号\\n操作人: 张三\\n复核人: 李四\\n工艺流程: 净选-分拣分装"
}
```

### 显示效果

上述数据将会显示为：

```
品名: 白术
批号: 20250220091

操作人: 张三
复核人: 李四

提醒: 品名: 白术
批号: 20250220091
日期: 2025年6月1号
操作人: 张三
复核人: 李四
工艺流程: 净选-分拣分装
```

## 测试页面

已创建 `test-timeline.html` 测试页面，包含：

1. **产品信息换行测试** - 展示在表单中如何处理包含换行符的文本
2. **时间线换行测试** - 展示在时间线中如何处理包含换行符的文本

## 兼容性说明

- 该功能向后兼容，不包含 `\n` 的普通文本仍按原方式显示
- 支持混合内容，同一个时间线节点中可以同时包含普通文本和换行文本
- 响应式设计，在移动设备上也能正常显示换行内容

## 使用建议

1. 在API数据中使用 `\\n` 标记需要换行的位置
2. 避免过长的单行文本，适当使用换行提高可读性
3. 对于结构化信息（如包含多个字段的描述），建议使用换行分隔

## 注意事项

- 换行符必须是 `\\n`（双反斜杠n），而不是真正的换行符
- 系统会自动检测并处理，无需额外配置
- 图片类型的属性不受此功能影响，仍按原方式显示
