# 🎵 手风琴效果实现说明

## 📋 需求描述
实现展开和折叠功能，每次只打开一个面板，比如打开第二个时自动折叠其他的。这包括：
1. 静态的"生产追溯"和"企业介绍"面板
2. 动态生成的产品信息面板（通过API获取）

## ✅ 已实现功能

### 1. 手风琴效果核心逻辑
- **位置**: `index.html` 第1430-1478行
- **功能**: 每次点击面板时，先关闭所有其他已展开的面板，然后切换当前面板状态
- **适用范围**: 所有折叠面板（静态和动态生成的）

### 2. 动态面板支持
- **位置**: `index.html` 第1421-1429行
- **功能**: 为动态生成的产品信息面板也应用手风琴效果
- **触发时机**: 当API数据加载完成，动态生成产品信息面板时

### 3. 初始化逻辑
- **位置**: `index.html` 第1406-1419行
- **功能**: 页面加载时初始化所有现有面板的手风琴效果
- **防重复**: 使用 `data-initialized` 属性防止重复绑定事件

## 🎯 关键实现细节

### 手风琴效果逻辑
```javascript
// 1. 先关闭所有其他面板
allExpandableHeaders.forEach(otherHeader => {
    if (otherHeader !== this) {
        // 折叠其他项目
        otherItem.classList.remove('active');
        otherIcon.classList.remove('bx-chevron-down');
        otherIcon.classList.add('bx-chevron-right');
    }
});

// 2. 切换当前面板状态
if (isActive) {
    // 折叠当前面板
    item.classList.remove('active');
    icon.classList.remove('bx-chevron-down');
    icon.classList.add('bx-chevron-right');
} else {
    // 展开当前面板
    item.classList.add('active');
    icon.classList.remove('bx-chevron-right');
    icon.classList.add('bx-chevron-down');
}
```

### CSS 控制
- 使用 `.expandable__item.active` 类控制面板展开状态
- 图标旋转：`bx-chevron-right` → `bx-chevron-down`
- 内容显示：`max-height: 0` → `max-height: 80vh`

## 📱 涉及的面板类型

### 1. 静态面板
- **生产追溯** (`production-trace`)
- **企业介绍** (`company-intro`)

### 2. 动态面板
- **基础信息** (通过 `apiData.ProductTemplate.PropertySessions` 生成)
- 每个 `SessionName` 对应一个独立的折叠面板
- 面板ID格式：`product-info-${index}`

## 🧪 测试方法

### 1. 使用测试页面
打开 `test-accordion-effect.html` 进行功能测试：
- 手动点击各个面板标题
- 使用"自动测试"按钮验证手风琴效果
- 观察是否每次只展开一个面板

### 2. 在主页面测试
1. 打开 `index.html`
2. 等待API数据加载完成
3. 依次点击各个面板标题
4. 验证手风琴效果是否正常

## 🔧 调试信息
代码中包含详细的控制台日志：
- `📂 展开面板: ${targetId}`
- `📁 折叠面板: ${targetId}`
- `🔄 手风琴效果：关闭其他面板 ${otherTargetId}`
- `✅ 为动态面板初始化手风琴功能`

## 📝 注意事项

1. **事件绑定防重复**: 使用 `data-initialized` 属性确保每个面板只绑定一次事件
2. **动态面板兼容**: 新生成的面板会自动应用手风琴效果
3. **图标状态同步**: 确保箭头图标与面板展开状态保持一致
4. **CSS兼容性**: 与现有的 `.expandable__item.active` CSS 规则完全兼容

## 🎉 效果预期
- ✅ 每次只能展开一个面板
- ✅ 点击已展开的面板可以折叠它
- ✅ 点击其他面板时，当前展开的面板会自动关闭
- ✅ 动态生成的产品信息面板也遵循手风琴效果
- ✅ 图标状态正确切换（右箭头 ↔ 下箭头）
