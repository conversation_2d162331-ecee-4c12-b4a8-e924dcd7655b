<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手风琴效果测试</title>
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>
    <link rel="stylesheet" href="assets/css/styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #13629b 0%, #0f4d7a 50%, #13629b 100%);
            min-height: 100vh;
            padding: 2rem;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .test-title {
            color: white;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        
        .test-description {
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .status-indicator {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 2rem;
            color: white;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎵 手风琴效果测试</h1>
        <div class="test-description">
            <p>测试说明：点击下面的任意一个面板标题，应该只展开该面板，同时自动关闭其他已展开的面板。</p>
            <p>这就是手风琴效果 - 每次只能展开一个面板。</p>
        </div>
        
        <div class="status-indicator" id="status">
            状态：等待测试...
        </div>
        
        <div class="about__expandable">
            <!-- 基础信息面板 -->
            <div class="expandable__item">
                <div class="expandable__header" data-target="basic-info">
                    <h3 class="expandable__title">📋 基础信息</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content" id="basic-info">
                    <div class="expandable__body">
                        <div class="form-row">
                            <label class="form-label">产品名称</label>
                            <input type="text" class="form-input" value="测试产品A" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">产品规格</label>
                            <input type="text" class="form-input" value="500ml" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">生产日期</label>
                            <input type="text" class="form-input" value="2024-01-15" readonly>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 生产追溯面板 -->
            <div class="expandable__item">
                <div class="expandable__header" data-target="production-trace">
                    <h3 class="expandable__title">🏭 生产追溯</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content" id="production-trace">
                    <div class="expandable__body">
                        <div class="form-row">
                            <label class="form-label">生产批号</label>
                            <input type="text" class="form-input" value="BATCH-2024-001" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">生产线</label>
                            <input type="text" class="form-input" value="生产线A" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">质检员</label>
                            <input type="text" class="form-input" value="张三" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">质检时间</label>
                            <input type="text" class="form-input" value="2024-01-15 14:30:00" readonly>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 企业介绍面板 -->
            <div class="expandable__item">
                <div class="expandable__header" data-target="company-intro">
                    <h3 class="expandable__title">🏢 企业介绍</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content" id="company-intro">
                    <div class="expandable__body">
                        <p>这是一家专业的食品生产企业，成立于2010年，致力于为消费者提供高质量的食品产品。</p>
                        <p>公司拥有先进的生产设备和严格的质量控制体系，确保每一件产品都符合国家标准。</p>
                        <p>我们的使命是：让每一位消费者都能享受到安全、健康、美味的食品。</p>
                    </div>
                </div>
            </div>

            <!-- 动态添加的面板（模拟API数据） -->
            <div class="expandable__item">
                <div class="expandable__header" data-target="dynamic-info">
                    <h3 class="expandable__title">⚡ 动态信息</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content" id="dynamic-info">
                    <div class="expandable__body">
                        <div class="form-row">
                            <label class="form-label">动态属性1</label>
                            <input type="text" class="form-input" value="动态值1" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">动态属性2</label>
                            <input type="text" class="form-input" value="动态值2" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 2rem; text-align: center;">
            <button onclick="testAccordion()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                🧪 自动测试手风琴效果
            </button>
            <button onclick="resetPanels()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                🔄 重置所有面板
            </button>
        </div>
    </div>

    <script>
        // 使用与index.html相同的手风琴逻辑
        function initExpandableItemForHeader(header) {
            header.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const targetContent = document.getElementById(targetId);
                const icon = this.querySelector('.expandable__icon');
                const item = this.closest('.expandable__item');

                if (targetContent && icon && item) {
                    const isActive = item.classList.contains('active');

                    // 🎯 手风琴效果：先关闭所有其他展开的项目
                    const allExpandableHeaders = document.querySelectorAll('.expandable__header');
                    
                    allExpandableHeaders.forEach(otherHeader => {
                        if (otherHeader !== this) {
                            const otherTargetId = otherHeader.getAttribute('data-target');
                            const otherIcon = otherHeader.querySelector('.expandable__icon');
                            const otherItem = otherHeader.closest('.expandable__item');
                            
                            if (otherItem && otherIcon) {
                                // 折叠其他项目
                                otherItem.classList.remove('active');
                                otherIcon.classList.remove('bx-chevron-down');
                                otherIcon.classList.add('bx-chevron-right');
                            }
                        }
                    });

                    // 切换当前项目的展开/折叠状态
                    if (isActive) {
                        // 如果当前项目已展开，则折叠它
                        item.classList.remove('active');
                        icon.classList.remove('bx-chevron-down');
                        icon.classList.add('bx-chevron-right');
                        updateStatus(`折叠面板: ${targetId}`);
                    } else {
                        // 如果当前项目未展开，则展开它
                        item.classList.add('active');
                        icon.classList.remove('bx-chevron-right');
                        icon.classList.add('bx-chevron-down');
                        updateStatus(`展开面板: ${targetId}（其他面板已自动关闭）`);
                    }
                }
            });
        }

        function initExpandableItems() {
            const expandableHeaders = document.querySelectorAll('.expandable__header');
            expandableHeaders.forEach(header => {
                if (!header.hasAttribute('data-initialized')) {
                    initExpandableItemForHeader(header);
                    header.setAttribute('data-initialized', 'true');
                }
            });
            console.log(`✅ 手风琴折叠面板功能已初始化，共 ${expandableHeaders.length} 个面板`);
        }

        function updateStatus(message) {
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.textContent = `状态：${message}`;
            }
        }

        function testAccordion() {
            const headers = document.querySelectorAll('.expandable__header');
            let index = 0;
            
            updateStatus('开始自动测试...');
            
            function clickNext() {
                if (index < headers.length) {
                    headers[index].click();
                    index++;
                    setTimeout(clickNext, 1500); // 每1.5秒点击下一个
                } else {
                    updateStatus('自动测试完成！如果每次只展开一个面板，说明手风琴效果正常工作。');
                }
            }
            
            clickNext();
        }

        function resetPanels() {
            const allItems = document.querySelectorAll('.expandable__item');
            const allIcons = document.querySelectorAll('.expandable__icon');
            
            allItems.forEach(item => item.classList.remove('active'));
            allIcons.forEach(icon => {
                icon.classList.remove('bx-chevron-down');
                icon.classList.add('bx-chevron-right');
            });
            
            updateStatus('所有面板已重置为折叠状态');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initExpandableItems();
            updateStatus('手风琴效果已初始化，请点击任意面板标题进行测试');
        });
    </script>
</body>
</html>
