<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颜色测试页面</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/timeline.css">
    <style>
        body {
            background-color: #F0E6C4 !important;
            padding: 2rem;
        }
        
        .color-demo {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        
        .primary-bg {
            background-color: #F0E6C4;
            color: #5D4E37;
        }
        
        .container-bg {
            background-color: #F8F0D8;
            color: #6B5B47;
        }
        
        .accent-bg {
            background-color: #D4B896;
            color: #5D4E37;
        }
        
        .text-demo {
            margin: 0.5rem 0;
        }
        
        .title-color {
            color: #5D4E37;
        }
        
        .text-color {
            color: #6B5B47;
        }
        
        .text-light-color {
            color: #8B7355;
        }
    </style>
</head>
<body>
    <h1 class="title-color">颜色主题测试</h1>
    
    <div class="color-demo primary-bg">
        <h3>主背景色 #F0E6C4</h3>
        <p>这是主要的背景颜色，用于页面整体背景。</p>
    </div>
    
    <div class="color-demo container-bg">
        <h3>容器背景色 #F8F0D8</h3>
        <p>这是容器的背景颜色，用于卡片和展开面板。</p>
    </div>
    
    <div class="color-demo accent-bg">
        <h3>强调色 #D4B896</h3>
        <p>这是强调色，用于按钮和活跃状态。</p>
    </div>
    
    <div class="color-demo container-bg">
        <h3>文字颜色测试</h3>
        <p class="text-demo title-color">标题颜色 #5D4E37 - 深棕色</p>
        <p class="text-demo text-color">正文颜色 #6B5B47 - 棕色</p>
        <p class="text-demo text-light-color">浅色文字 #8B7355 - 浅棕色</p>
    </div>
    
    <div class="color-demo container-bg">
        <h3>导航按钮测试</h3>
        <div style="background-color: #F8F0D8; padding: 1rem; border-radius: 0.5rem; margin: 0.5rem 0;">
            <span style="color: #6B5B47;">普通状态</span>
        </div>
        <div style="background: linear-gradient(180deg, #D4B896, rgba(212, 184, 150, 0.3)); padding: 1rem; border-radius: 0.5rem; margin: 0.5rem 0;">
            <span style="color: #5D4E37;">活跃状态</span>
        </div>
        <div style="background: rgba(212, 184, 150, 0.2); padding: 1rem; border-radius: 0.5rem; margin: 0.5rem 0;">
            <span style="color: #6B5B47;">悬停状态</span>
        </div>
    </div>
    
    <div class="color-demo container-bg">
        <h3>表单元素测试</h3>
        <label style="color: #5D4E37; display: block; margin: 0.5rem 0;">表单标签</label>
        <input type="text" value="表单输入框" style="background-color: #F8F0D8; border: 1px solid #D4B896; color: #6B5B47; padding: 0.5rem; border-radius: 0.25rem; width: 100%;">
    </div>
    
    <div class="color-demo container-bg">
        <h3>时间线元素测试</h3>
        <div style="background-color: #F8F0D8; border: 1px solid #D4B896; border-radius: 0.5rem; padding: 1rem; margin: 0.5rem 0;">
            <div style="background-color: #F0E6C4; padding: 0.75rem; border-bottom: 1px solid #D4B896; margin-bottom: 0.5rem;">
                <span style="color: #5D4E37; font-weight: bold;">2024-01-15</span>
                <span style="color: #8B7355; margin-left: 1rem;">生产节点</span>
            </div>
            <div style="padding: 0.5rem;">
                <span style="color: #8B7355;">标签：</span>
                <span style="color: #5D4E37;">详细信息内容</span>
            </div>
        </div>
    </div>
</body>
</html>
