<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间线换行测试</title>
    <link rel="stylesheet" href="assets/css/timeline.css">
    <style>
        body {
            background-color: #F0E6C4;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #5D4E37;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            margin-top: 40px;
            margin-bottom: 20px;
        }

        /* 表单样式 */
        .form-row {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
            gap: 1rem;
        }
        .form-label {
            color: #5D4E37;
            font-weight: 600;
            min-width: 120px;
            flex-shrink: 0;
            padding-top: 0.5rem;
        }
        .form-input {
            color: #6B5B47;
            background-color: #F8F0D8;
            border: 1px solid rgba(93, 78, 55, 0.2);
            border-radius: 4px;
            padding: 0.5rem;
            flex: 1;
            font-family: inherit;
        }
        .form-textarea {
            min-height: 60px !important;
            resize: vertical !important;
            font-family: inherit !important;
            line-height: 1.5 !important;
            white-space: pre-wrap !important;
        }
        .product-info-section {
            background-color: #F8F0D8;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #D4B896;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>换行功能测试</h1>

        <h2>产品信息换行测试</h2>
        <div class="product-info-section" id="product-info-section">
            <!-- 产品信息将通过JavaScript生成 -->
        </div>

        <h2>时间线换行测试</h2>
        <div class="timeline-container" id="timeline-container">
            <!-- 时间线内容将通过JavaScript生成 -->
        </div>
    </div>

    <script>
        // 测试数据 - 模拟包含换行符的API数据
        const testApiData = {
            EntCode: 'TEST001',
            // 产品信息测试数据
            ProductTemplate: {
                PropertySessions: [
                    {
                        SessionName: '基本信息',
                        Properties: [
                            {
                                PropertyName: '产品名称',
                                PropertyType: 0
                            },
                            {
                                PropertyName: '详细描述',
                                PropertyType: 0
                            },
                            {
                                PropertyName: '规格说明',
                                PropertyType: 0
                            }
                        ]
                    }
                ]
            },
            ProductProperties: {
                '基本信息_产品名称': '白术片',
                '基本信息_详细描述': '品名: 白术\\n批号: 20250220091\\n日期: 2025年6月1号\\n操作人: 张三\\n复核人: 李四',
                '基本信息_规格说明': '规格: 500g/袋\\n包装: 密封袋装\\n存储: 阴凉干燥处保存'
            },
            LifeCycleNodes: [
                {
                    NodeName: '生产过程信息',
                    Properties: [
                        {
                            PropertyName: '开始时间',
                            PropertyType: 0
                        },
                        {
                            PropertyName: '品名',
                            PropertyType: 0
                        },
                        {
                            PropertyName: '日期',
                            PropertyType: 0
                        },
                        {
                            PropertyName: '操作人',
                            PropertyType: 0
                        },
                        {
                            PropertyName: '工艺流程',
                            PropertyType: 0
                        }
                    ]
                },
                {
                    NodeName: '开始验证',
                    Properties: [
                        {
                            PropertyName: '开始时间',
                            PropertyType: 0
                        },
                        {
                            PropertyName: '提醒',
                            PropertyType: 0
                        },
                        {
                            PropertyName: '操作人',
                            PropertyType: 0
                        },
                        {
                            PropertyName: '工艺流程',
                            PropertyType: 0
                        }
                    ]
                }
            ],
            LifeCycles: {
                '生产过程信息_开始时间': '2025年6月1号',
                '生产过程信息_品名': '白术\\n批号: 20250220091',
                '生产过程信息_日期': '2025年6月1号',
                '生产过程信息_操作人': '张三\\n复核人: 李四',
                '生产过程信息_工艺流程': '净选-分拣分装',
                
                '开始验证_开始时间': '2025年8月1号',
                '开始验证_提醒': '品名: 白术\\n批号: 20250220091\\n日期: 2025年6月1号\\n操作人: 张三\\n复核人: 李四\\n工艺流程: 净选-分拣分装',
                '开始验证_操作人': '张三\\n复核人: 李四',
                '开始验证_工艺流程': '净选-分拣分装'
            }
        };

        // 动态生成时间线的函数（简化版，专门用于测试）
        function generateTestTimeline(apiData) {
            try {
                const timelineContainer = document.getElementById('timeline-container');
                if (!timelineContainer) {
                    console.error('找不到时间线容器');
                    return;
                }

                // 获取生命周期节点和数据
                const lifeCycleNodes = apiData.LifeCycleNodes || [];
                const lifeCycles = apiData.LifeCycles || {};
                const entCode = apiData.EntCode || 'TEST';

                console.log('LifeCycleNodes:', lifeCycleNodes);
                console.log('LifeCycles:', lifeCycles);

                // 清空现有内容
                timelineContainer.innerHTML = '';

                if (lifeCycleNodes.length === 0) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">暂无时间线数据</div>';
                    return;
                }

                // 遍历每个生命周期节点
                lifeCycleNodes.forEach((node, index) => {
                    const nodeName = node.NodeName || `节点${index + 1}`;
                    const properties = node.Properties || [];

                    // 获取开始时间
                    const startTimeKey = `${nodeName}_开始时间`;
                    const startTime = lifeCycles[startTimeKey] || '未知时间';

                    // 创建时间线项目
                    const timelineItem = document.createElement('div');
                    timelineItem.className = 'timeline-item';
                    timelineItem.id = `timeline-${nodeName.replace(/\s+/g, '-')}`;

                    // 创建时间线日期
                    const timelineDate = document.createElement('div');
                    timelineDate.className = 'timeline-date';
                    timelineDate.innerHTML = `<span>${startTime}</span> ${nodeName}`;

                    // 创建时间线内容
                    const timelineContent = document.createElement('div');
                    timelineContent.className = 'timeline-content';

                    const timelineDetails = document.createElement('div');
                    timelineDetails.className = 'timeline-details';

                    // 遍历属性
                    properties.forEach(property => {
                        const propertyName = property.PropertyName || '';
                        const propertyType = property.PropertyType || 0;

                        if (!propertyName || propertyName === '开始时间') return; // 跳过开始时间属性

                        // 构建查找键：NodeName_PropertyName（去掉冒号）
                        const cleanPropertyName = propertyName.replace(/：$/, '');
                        const lookupKey = `${nodeName}_${cleanPropertyName}`;
                        let value = lifeCycles[lookupKey];

                        // 如果没找到，尝试带冒号的版本
                        if (!value && propertyName.endsWith('：')) {
                            const lookupKeyWithColon = `${nodeName}_${propertyName}`;
                            value = lifeCycles[lookupKeyWithColon];
                        }

                        if (!value) {
                            console.log(`未找到数据: ${lookupKey}`);
                            return; // 如果没有值，跳过
                        }

                        // 创建详情行
                        const detailRow = document.createElement('div');
                        detailRow.className = 'detail-row';

                        const detailLabel = document.createElement('span');
                        detailLabel.className = 'detail-label';
                        detailLabel.textContent = propertyName.endsWith('：') ? propertyName : `${propertyName}：`;

                        const detailValue = document.createElement('span');
                        detailValue.className = 'detail-value';

                        // 根据属性类型处理值
                        if (propertyType === 7) {
                            // 图片类型（测试中暂不处理）
                            detailValue.textContent = '图片类型（测试中不显示）';
                        } else {
                            // 文本类型 - 处理换行符
                            if (typeof value === 'string' && value.includes('\\n')) {
                                // 如果包含 \n，将其转换为真正的换行
                                const lines = value.split('\\n');
                                lines.forEach((line, lineIndex) => {
                                    if (lineIndex > 0) {
                                        // 添加换行元素
                                        detailValue.appendChild(document.createElement('br'));
                                    }
                                    // 添加文本节点
                                    const textNode = document.createTextNode(line);
                                    detailValue.appendChild(textNode);
                                });
                            } else {
                                // 普通文本，直接设置
                                detailValue.textContent = value;
                            }
                        }

                        detailRow.appendChild(detailLabel);
                        detailRow.appendChild(detailValue);
                        timelineDetails.appendChild(detailRow);
                    });

                    // 只有当有内容时才添加时间线项目
                    if (timelineDetails.children.length > 0) {
                        timelineContent.appendChild(timelineDetails);
                        timelineItem.appendChild(timelineDate);
                        timelineItem.appendChild(timelineContent);
                        timelineContainer.appendChild(timelineItem);
                    }
                });

                console.log('✅ 测试时间线已生成，共', lifeCycleNodes.length, '个节点');

            } catch (error) {
                console.error('生成测试时间线时发生错误:', error);
                const timelineContainer = document.getElementById('timeline-container');
                if (timelineContainer) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">时间线生成失败</div>';
                }
            }
        }

        // 生成测试产品信息的函数
        function generateTestProductInfo(apiData) {
            try {
                const productInfoSection = document.getElementById('product-info-section');
                if (!productInfoSection) {
                    console.error('找不到产品信息容器');
                    return;
                }

                const productTemplate = apiData.ProductTemplate || {};
                const propertySessions = productTemplate.PropertySessions || [];
                const productProperties = apiData.ProductProperties || {};

                // 清空现有内容
                productInfoSection.innerHTML = '';

                if (propertySessions.length === 0) {
                    productInfoSection.innerHTML = '<div>暂无产品信息</div>';
                    return;
                }

                // 遍历PropertySessions数组
                propertySessions.forEach((session, index) => {
                    const sessionName = session.SessionName || `产品信息${index + 1}`;
                    const properties = session.Properties || [];

                    console.log(`创建产品信息: ${sessionName}`);

                    properties.forEach(property => {
                        const propertyName = property.PropertyName || '';
                        const propertyType = property.PropertyType || 0;
                        if (!propertyName) return;

                        // 构建查找键
                        const lookupKey = `${sessionName}_${propertyName}`;
                        const propertyValue = productProperties[lookupKey] || '暂无数据';

                        console.log(`查找键: ${lookupKey}, 值: ${propertyValue}, 类型: ${propertyType}`);

                        // 创建表单行
                        const formRow = document.createElement('div');
                        formRow.className = 'form-row';

                        const label = document.createElement('label');
                        label.className = 'form-label';
                        label.textContent = propertyName;

                        if (propertyType === 7) {
                            // 图片类型（测试中暂不处理）
                            const input = document.createElement('input');
                            input.className = 'form-input';
                            input.type = 'text';
                            input.value = '图片类型（测试中不显示）';
                            input.readOnly = true;
                            formRow.appendChild(label);
                            formRow.appendChild(input);
                        } else {
                            // 文本类型 - 检查是否包含换行符
                            if (typeof propertyValue === 'string' && propertyValue.includes('\\n')) {
                                // 包含换行符，使用textarea
                                const textarea = document.createElement('textarea');
                                textarea.className = 'form-input form-textarea';
                                textarea.value = propertyValue.replace(/\\n/g, '\n');
                                textarea.readOnly = true;
                                formRow.appendChild(label);
                                formRow.appendChild(textarea);
                            } else {
                                // 普通文本，使用input
                                const input = document.createElement('input');
                                input.className = 'form-input';
                                input.type = 'text';
                                input.value = propertyValue;
                                input.readOnly = true;
                                formRow.appendChild(label);
                                formRow.appendChild(input);
                            }
                        }

                        productInfoSection.appendChild(formRow);
                    });
                });

                console.log('✅ 测试产品信息已生成');

            } catch (error) {
                console.error('生成测试产品信息时发生错误:', error);
                const productInfoSection = document.getElementById('product-info-section');
                if (productInfoSection) {
                    productInfoSection.innerHTML = '<div>产品信息生成失败</div>';
                }
            }
        }

        // 页面加载完成后生成测试内容
        document.addEventListener('DOMContentLoaded', function() {
            generateTestProductInfo(testApiData);
            generateTestTimeline(testApiData);
        });
    </script>
</body>
</html>
