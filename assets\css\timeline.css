/* 时间线样式 - 基于图片设计 */
.timeline-container {
  position: relative;
  padding-left: 3rem;
  margin-top: 2rem;
}

/* 垂直连接线 */
.timeline-container::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #D4B896;
  z-index: 1;
}

/* 时间线项目 */
.timeline-item {
  position: relative;
  margin-bottom: 2.5rem;
  background-color: #F8F0D8;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(93, 78, 55, 0.15);
  border: 1px solid #D4B896;
  transition: all 0.3s ease;
}

.timeline-item:hover {
  box-shadow: 0 4px 20px rgba(93, 78, 55, 0.25);
  transform: translateY(-2px);
}

/* 圆形标记点 */
.timeline-item::before {
  content: '';
  position: absolute;
  left: -2.25rem;
  top: 1.5rem;
  width: 12px;
  height: 12px;
  background-color: #F8F0D8;
  border: 3px solid #D4B896;
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 0 0 4px #F8F0D8;
}

/* 连接箭头 */
.timeline-item::after {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 1.5rem;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid #F8F0D8;
  z-index: 1;
}

/* 时间线日期头部 */
.timeline-date {
  background-color: #F0E6C4;
  padding: 1.25rem 1.5rem 1rem;
  color: #8B7355;
  font-size: 0.875rem;
  font-weight: 600;
  border-bottom: 1px solid #D4B896;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.timeline-date span {
  color: #5D4E37;
  font-weight: 700;
  font-size: 1rem;
}

/* 时间线内容区域 */
.timeline-content {
  padding: 1.5rem;
  background-color: #F8F0D8;
}

.timeline-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 详情行 */
.detail-row {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f8f9fa;
  gap: 0.5rem;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #8B7355;
  font-size: 0.875rem;
  line-height: 1.4;
  min-width: 5rem;
  flex-shrink: 0;
  white-space: nowrap;
}

.detail-value {
  color: #5D4E37;
  font-size: 0.875rem;
  line-height: 1.4;
  text-align: left;
  word-break: break-word;
  flex: 1;
  margin-left: 0.5rem;
}

/* 时间线图片样式 */
.detail-value img {
  max-width: 200px;
  max-height: 150px;
  object-fit: contain;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 5px;
  display: block;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-value img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 加载状态样式 */
.timeline-loading {
  text-align: center;
  padding: 2rem;
  color: #8B7355;
  font-style: italic;
}

/* 深色主题样式 */
body:not(.light-theme) .timeline-container::before {
  background-color: rgba(255, 255, 255, 0.15);
}

body:not(.light-theme) .timeline-item {
  background-color: var(--container-color);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
}

body:not(.light-theme) .timeline-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.12);
}

body:not(.light-theme) .timeline-item::before {
  background-color: var(--container-color);
  border-color: var(--first-color);
  box-shadow: 0 0 0 4px var(--container-color);
}

body:not(.light-theme) .timeline-item::after {
  border-right-color: var(--container-color);
}

body:not(.light-theme) .timeline-date {
  background-color: rgba(255, 255, 255, 0.03);
  color: var(--text-color-light);
  border-bottom-color: rgba(255, 255, 255, 0.08);
}

body:not(.light-theme) .timeline-date span {
  color: var(--title-color);
}

body:not(.light-theme) .timeline-content {
  background-color: var(--container-color);
}

body:not(.light-theme) .detail-label {
  color: var(--text-color-light);
}

body:not(.light-theme) .detail-value {
  color: var(--text-color);
}

body:not(.light-theme) .detail-row {
  border-bottom-color: rgba(255, 255, 255, 0.06);
}

/* 深色主题图片样式 */
body:not(.light-theme) .detail-value img {
  border-color: rgba(255, 255, 255, 0.2);
}

body:not(.light-theme) .detail-value img:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
}

/* 深色主题加载状态 */
body:not(.light-theme) .timeline-loading {
  color: var(--text-color-light);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .timeline-container {
    padding-left: 2rem;
  }

  .timeline-container::before {
    left: 1rem;
  }

  .timeline-item::before {
    left: -1.75rem;
  }

  .timeline-item::after {
    left: -1rem;
  }

  .timeline-date {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .detail-row {
    gap: 0.5rem;
    align-items: flex-start;
    padding: 0.5rem 0;
  }

  .detail-label {
    min-width: 4.5rem;
    flex-shrink: 0;
    font-size: 0.8rem;
  }

  .detail-value {
    text-align: left;
    font-size: 0.8rem;
    margin-left: 0.25rem;
  }

  /* 移动端图片样式 */
  .detail-value img {
    max-width: 150px;
    max-height: 120px;
  }
}

/* 中等屏幕优化 */
@media screen and (max-width: 600px) {
  .detail-label {
    min-width: 5rem;
    font-size: 0.8rem;
  }

  .detail-value {
    font-size: 0.8rem;
  }
}

@media screen and (max-width: 480px) {
  .timeline-container {
    padding-left: 1.5rem;
  }

  .timeline-container::before {
    left: 0.75rem;
  }

  .timeline-item::before {
    left: -1.5rem;
  }

  .timeline-item::after {
    left: -0.75rem;
  }

  .timeline-date {
    padding: 1rem;
  }

  .timeline-content {
    padding: 1rem;
  }

  .detail-row {
    padding: 0.4rem 0;
    gap: 0.4rem;
  }

  .detail-label {
    min-width: 4rem;
    font-size: 0.75rem;
  }

  .detail-value {
    font-size: 0.75rem;
    margin-left: 0.2rem;
  }

  /* 小屏幕图片样式 */
  .detail-value img {
    max-width: 120px;
    max-height: 100px;
  }
}

/* 超小屏幕优化 (iPhone SE等) */
@media screen and (max-width: 320px) {
  .timeline-container {
    padding-left: 1.2rem;
  }

  .timeline-container::before {
    left: 0.6rem;
  }

  .timeline-item::before {
    left: -1.3rem;
  }

  .timeline-item::after {
    left: -0.6rem;
  }

  .detail-row {
    padding: 0.3rem 0;
    gap: 0.3rem;
  }

  .detail-label {
    min-width: 3.8rem;
    font-size: 0.7rem;
  }

  .detail-value {
    font-size: 0.7rem;
    margin-left: 0.1rem;
  }

  .timeline-date {
    padding: 0.8rem;
    font-size: 0.75rem;
  }

  .timeline-content {
    padding: 0.8rem;
  }

  /* 超小屏幕图片样式 */
  .detail-value img {
    max-width: 100px;
    max-height: 80px;
  }
}